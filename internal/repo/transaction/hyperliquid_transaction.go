package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// HyperLiquidTransactionRepository defines the interface for interacting with HyperLiquid transactions
type HyperLiquidTransactionRepositoryInterface interface {
	Create(ctx context.Context, tx *model.HyperLiquidTransaction) error
	BulkCreate(ctx context.Context, txs []model.HyperLiquidTransaction) error
	UpdateByCloid(ctx context.Context, cloid string, tx *model.HyperLiquidTransaction) error
	BulkUpsertByCloid(ctx context.Context, txs []model.HyperLiquidTransaction) error
	FindByCloid(ctx context.Context, cloid string) (*model.HyperLiquidTransaction, error)
	GetTotalVolumeByUserIDs(ctx context.Context, userIDs []uuid.UUID) (decimal.Decimal, error)
	GetTransactingUserIDs(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error)
	GetVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error)
}

type HyperLiquidTransactionRepository struct {
	db *gorm.DB
}

func NewHyperLiquidTransactionRepository() HyperLiquidTransactionRepositoryInterface {
	return &HyperLiquidTransactionRepository{
		db: global.GVA_DB,
	}
}

func (r *HyperLiquidTransactionRepository) Create(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	transaction := r.db.WithContext(ctx).Model(&model.HyperLiquidTransaction{}).Create(tx)

	if transaction.Error != nil {
		return transaction.Error
	}

	return nil
}

func (r *HyperLiquidTransactionRepository) BulkCreate(ctx context.Context, txs []model.HyperLiquidTransaction) error {
	if len(txs) == 0 {
		return nil
	}

	// Use the database instance with context for bulk creation
	if err := r.db.WithContext(ctx).Model(&model.HyperLiquidTransaction{}).
		Clauses(clause.OnConflict{DoNothing: true}).
		CreateInBatches(txs, 100).Error; err != nil {
		return err
	}

	return nil
}

func (r *HyperLiquidTransactionRepository) UpdateByCloid(ctx context.Context, cloid string, tx *model.HyperLiquidTransaction) error {
	result := r.db.WithContext(ctx).Model(&model.HyperLiquidTransaction{}).
		Where("cloid = ?", cloid).
		Updates(tx)

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no record found with cloid: %s", cloid)
	}

	return nil
}

func (r *HyperLiquidTransactionRepository) BulkUpsertByCloid(ctx context.Context, txs []model.HyperLiquidTransaction) error {
	if len(txs) == 0 {
		return nil
	}

	// Use upsert with cloid as the conflict key
	if err := r.db.WithContext(ctx).Model(&model.HyperLiquidTransaction{}).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "cloid"}},
			UpdateAll: true,
		}).
		CreateInBatches(txs, 100).Error; err != nil {
		return err
	}

	return nil
}

func (r *HyperLiquidTransactionRepository) FindByCloid(ctx context.Context, cloid string) (*model.HyperLiquidTransaction, error) {
	var tx model.HyperLiquidTransaction
	result := r.db.WithContext(ctx).Where("cloid = ?", cloid).First(&tx)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil // 返回 nil 表示记录不存在
		}
		return nil, result.Error
	}

	return &tx, nil
}

// GetTotalVolumeByUserIDs calculates the total volume from hyperliquid transactions for given user IDs
func (r *HyperLiquidTransactionRepository) GetTotalVolumeByUserIDs(ctx context.Context, userIDs []uuid.UUID) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.HyperLiquidTransaction{}).
		Select("COALESCE(SUM(avg_price * size), 0) as total_volume").
		Where("user_id IN ?", userIDs).
		Where("status = ?", "completed").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get total volume: %w", err)
	}

	return result.TotalVolume, nil
}

// GetTransactingUserIDs gets the list of user IDs who have made transactions
func (r *HyperLiquidTransactionRepository) GetTransactingUserIDs(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error) {
	if len(userIDs) == 0 {
		return []uuid.UUID{}, nil
	}

	var transactingUserIDs []uuid.UUID

	err := r.db.WithContext(ctx).
		Model(&model.HyperLiquidTransaction{}).
		Distinct("user_id").
		Where("user_id IN ?", userIDs).
		Where("status = ?", "completed").
		Pluck("user_id", &transactingUserIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get transacting user IDs: %w", err)
	}

	return transactingUserIDs, nil
}

// GetVolumeByUserIDsAndPeriod calculates the total volume from hyperliquid transactions for given user IDs within a time period
func (r *HyperLiquidTransactionRepository) GetVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	err := r.db.WithContext(ctx).
		Model(&model.HyperLiquidTransaction{}).
		Select("COALESCE(SUM(avg_price * size), 0) as total_volume").
		Where("user_id IN ?", userIDs).
		Where("status = ?", "filled").
		Where("created_at::timestamptz >= ?::timestamp AND created_at::timestamptz < ?::timestamp", startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get total volume by period: %w", err)
	}

	return result.TotalVolume, nil
}
