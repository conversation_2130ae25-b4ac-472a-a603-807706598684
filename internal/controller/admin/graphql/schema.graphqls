scalar Time
scalar Int64

directive @adminAuth on FIELD_DEFINITION

type Query {
  # Admin: Get all tasks
  adminGetAllTasks: [ActivityTask!]! @adminAuth

  # Admin: Get task completion statistics
  adminGetTaskCompletionStats(input: AdminStatsInput!): AdminTaskCompletionStatsResponse! @adminAuth

  # Admin: Get user activity statistics
  adminGetUserActivityStats(input: AdminStatsInput!): AdminUserActivityStatsResponse! @adminAuth

  # Admin: Get tier distribution
  adminGetTierDistribution: AdminTierDistributionResponse! @adminAuth

  # Admin: Get top users by points
  adminGetTopUsers(limit: Int = 10): [UserTierInfo!]! @adminAuth

  # Agent Referral Admin Queries
  adminGetInfiniteAgentById(id: ID!): AdminInfiniteAgentConfig @adminAuth
  adminGetAllInfiniteAgents(input: GetInfiniteAgentsInput!): AdminInfiniteAgentsResponse! @adminAuth
  adminGetAllAgentLevels: [AgentLevelWithStats!]! @adminAuth
  adminSearchUsersByInvitationCode(invitationCode: String!): [User!]! @adminAuth
  adminGetUserReferralSnapshot(userId: ID!): ReferralSnapshotFull @adminAuth
  adminGetUsersByAgentLevel(input: GetUsersByLevelInput!): UsersByLevelResponse! @adminAuth
  adminGetAllReferralTrees(input: GetReferralTreesInput!): ReferralTreesResponse! @adminAuth
  adminGetReferralTreeById(id: ID!): InfiniteAgentReferralTree @adminAuth
  adminGetAgentReferralStats(input: AdminStatsDateInput!): AgentReferralStats! @adminAuth
  adminGetCommissionDistributionStats(input: AdminStatsDateInput!): CommissionDistributionStats! @adminAuth
  adminGetTopPerformingAgents(input: GetTopAgentsInput!): [TopAgent!]! @adminAuth
}

type Mutation {
  # Admin: Create task
  createTask(input: CreateTaskInput!): ActivityTask! @adminAuth

  # Admin: Create consecutive check-in task with configurable milestones
  createConsecutiveCheckinTask(input: CreateConsecutiveCheckinTaskInput!): ActivityTask! @adminAuth

  # Admin: Update task
  updateTask(input: UpdateTaskInput!): ActivityTask! @adminAuth

  # Admin: Delete task
  deleteTask(taskId: ID!): Boolean! @adminAuth

  # Admin: Create task category
  createTaskCategory(input: CreateTaskCategoryInput!): TaskCategory! @adminAuth

  # Admin: Update task category
  updateTaskCategory(input: UpdateTaskCategoryInput!): TaskCategory! @adminAuth

  # Admin: Delete task category
  deleteTaskCategory(categoryId: ID!): Boolean! @adminAuth

  # Admin: Create tier benefit
  createTierBenefit(input: CreateTierBenefitInput!): TierBenefitResponse! @adminAuth

  # Admin: Update tier benefit
  updateTierBenefit(input: UpdateTierBenefitInput!): TierBenefitResponse! @adminAuth

  # Admin: Delete tier benefit
  deleteTierBenefit(tierBenefitId: ID!): Boolean! @adminAuth

  # Admin: Reset daily tasks
  adminResetDailyTasks: Boolean! @adminAuth

  # Admin: Reset weekly tasks
  adminResetWeeklyTasks: Boolean! @adminAuth

  # Admin: Reset monthly tasks
  adminResetMonthlyTasks: Boolean! @adminAuth

  # Admin: Recalculate all user tiers
  adminRecalculateAllUserTiers: Boolean! @adminAuth

  # Admin: Seed initial tasks
  adminSeedInitialTasks: Boolean! @adminAuth

  # Agent Level CRUD operations
  adminCreateAgentLevel(input: CreateAgentLevelInput!): CreateAgentLevelResponse! @adminAuth
  adminUpdateAgentLevel(input: UpdateAgentLevelInput!): UpdateAgentLevelResponse! @adminAuth
  adminDeleteAgentLevel(id: Int!): DeleteAgentLevelResponse! @adminAuth

  # Agent Referral Admin Mutations
  adminCreateInfiniteAgent(input: CreateInfiniteAgentInput!): AdminInfiniteAgentConfig! @adminAuth
  adminUpdateInfiniteAgent(input: UpdateInfiniteAgentInput!): AdminInfiniteAgentConfig! @adminAuth
  adminDeleteInfiniteAgent(id: ID!): Boolean! @adminAuth
  adminUpdateAgentLevelCommissionRates(input: UpdateCommissionRatesInput!): AgentLevelWithStats! @adminAuth
  adminCreateReferralTreeSnapshot(input: CreateTreeSnapshotInput!): InfiniteAgentReferralTree! @adminAuth
  adminDeleteReferralTree(id: ID!): Boolean! @adminAuth
  adminRecalculateAllReferralSnapshots: SystemOperationResult! @adminAuth
  adminRecalculateAllInfiniteAgentCommissions: SystemOperationResult! @adminAuth
  adminSyncReferralTreeData: SystemOperationResult! @adminAuth
}

# Agent Level CRUD Input Types
input CreateAgentLevelInput {
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
}

input UpdateAgentLevelInput {
  id: Int!
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
}

# Agent Level CRUD Response Types
type CreateAgentLevelResponse {
  level: AgentLevel
  success: Boolean!
  message: String!
}

type UpdateAgentLevelResponse {
  level: AgentLevel
  success: Boolean!
  message: String!
}

type DeleteAgentLevelResponse {
  success: Boolean!
  message: String!
}

# Common Types
type User {
  id: ID!
  email: String
  invitationCode: String
  createdAt: String!
  updatedAt: String!
  deletedAt: String
  agentLevelId: Int!
  agentLevel: AgentLevel!
  levelGracePeriodStartedAt: String
  levelUpgradedAt: String
  firstTransactionAt: String
}

type AgentLevel {
  id: Int!
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
}

type ReferralSnapshotFull {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  tradingUserCount: Int!
  totalPerpsVolumeUsd: Float!
  totalPerpsFees: Float!
  totalPerpsFeesPaid: Float!
  totalPerpsFeesUnpaid: Float!
  totalPerpsTradesCount: Int!
  totalMemeVolumeUsd: Float!
  totalMemeFees: Float!
  totalMemeFeesPaid: Float!
  totalMemeFeesUnpaid: Float!
  totalMemeTradesCount: Int!
  totalCommissionEarnedUsd: Float!
  claimedCommissionUsd: Float!
  unclaimedCommissionUsd: Float!
  totalCashbackEarnedUsd: Float!
  claimedCashbackUsd: Float!
  unclaimedCashbackUsd: Float!
  l1UplineId: ID
  l2UplineId: ID
  l3UplineId: ID
}

type InfiniteAgentReferralTree {
  id: ID!
  createdAt: Time!
  infiniteAgentUserId: ID!
  commissionRateN: Float!
  rootUserId: ID!
  snapshotDate: Time!
  totalNodes: Int!
  maxDepth: Int!
  directCount: Int!
  activeUsers: Int!
  tradingUsers: Int!
  totalCommissionEarned: Float!
  totalVolumeUsd: Float!
  status: String!
  description: String
}

# Agent Referral Types
type AdminInfiniteAgentConfig {
  id: ID!
  userId: ID!
  user: User
  commissionRateN: Float!
  totalNetFeeUsd: Float!
  totalStandardCommissionPaidUsd: Float!
  finalCommissionAmountUsd: Float!
  memeVolumeUsd: Float!
  memePaidCommissionUsd: Float!
  memeNetFeeUsd: Float!
  contractTotalFeeUsd: Float!
  contractPaidCommissionUsd: Float!
  contractNetFeeUsd: Float!
  status: String!
  createdAt: Time!
  updatedAt: Time!
}

type AdminInfiniteAgentsResponse {
  agents: [AdminInfiniteAgentConfig!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}

type AgentLevelWithStats {
  id: Int!
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
  userCount: Int!
  totalVolumeUsd: Float!
  totalCommission: Float!
}

type UsersByLevelResponse {
  users: [User!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}

type ReferralTreesResponse {
  trees: [InfiniteAgentReferralTree!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}

type AgentReferralStats {
  totalUsers: Int!
  totalInfiniteAgents: Int!
  totalVolumeUsd: Float!
  totalCommissionPaid: Float!
  activeReferralTrees: Int!
  averageTreeSize: Float!
}

type CommissionDistributionStats {
  directCommission: Float!
  indirectCommission: Float!
  extendedCommission: Float!
  infiniteCommission: Float!
  totalCommission: Float!
}

type TopAgent {
  userId: ID!
  invitationCode: String!
  email: String
  agentLevel: String!
  commissionEarned: Float!
  volumeGenerated: Float!
  referralCount: Int!
  isInfiniteAgent: Boolean!
}

type SystemOperationResult {
  success: Boolean!
  processedCount: Int!
  errorCount: Int!
  message: String!
}

# Agent Referral Input Types
input CreateInfiniteAgentInput {
  userId: ID!
  commissionRateN: Float!
  status: String!
}

input UpdateInfiniteAgentInput {
  id: ID!
  commissionRateN: Float
  status: String
}

input GetInfiniteAgentsInput {
  page: Int = 1
  pageSize: Int = 10
  status: String
  sortBy: String
  sortOrder: String = "DESC"
}

input UpdateCommissionRatesInput {
  levelId: Int!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
}

input GetUsersByLevelInput {
  levelId: Int!
  page: Int = 1
  pageSize: Int = 10
  sortBy: String
  sortOrder: String = "DESC"
}

input GetReferralTreesInput {
  page: Int = 1
  pageSize: Int = 10
  sortBy: String
  sortOrder: String = "DESC"
}

input CreateTreeSnapshotInput {
  rootUserId: ID!
}

input AdminStatsDateInput {
  startDate: Time!
  endDate: Time!
}

input GetTopAgentsInput {
  limit: Int = 10
  sortBy: String!
}
