# Data Overview schema

type DataOverviewCategory {
  all: Float!
  meme: Float!
  contract: Float!
}

type DataOverview {
  rebateAmount: DataOverviewCategory!
  transactionVolume: DataOverviewCategory!
  invitationCount: DataOverviewCategory!
  timestamp: Time!
  period: String!
}

enum DataOverviewType {
  ALL
  MEME
  CONTRACT
}

enum DataOverviewTimeRange {
  TODAY
  LAST_30_DAYS
  LAST_60_DAYS
  ALL_TIME
}

input DataOverviewInput {
  timeRange: DataOverviewTimeRange!
}

type DataOverviewResponse {
  data: [DataOverview!]!
  success: Boolean!
  message: String
}

type DataOverviewSummary {
  totalRebateAmount: DataOverviewCategory!
  totalTransactionVolume: DataOverviewCategory!
  totalInvitationCount: DataOverviewCategory!
  peakRebateAmount: DataOverviewCategory!
  peakTransactionVolume: DataOverviewCategory!
  peakInvitationCount: DataOverviewCategory!
  averageRebateAmount: DataOverviewCategory!
  averageTransactionVolume: DataOverviewCategory!
  averageInvitationCount: DataOverviewCategory!
}

type DataOverviewWithSummary {
  data: [DataOverview!]!
  summary: DataOverviewSummary!
  success: Boolean!
  message: String
}

# Chart data point for rebate amount
type RebateAmountChartDataPoint {
  timestamp: Time!
  period: String!
  contract: String!
  meme: String!
  all: String!
}

# Current values for rebate amount chart
type RebateAmountCurrentValues {
  all: String!
  meme: String!
  contract: String!
}

# Response for rebate amount chart
type RebateAmountChartResponse {
  currentValues: RebateAmountCurrentValues!
  data: [RebateAmountChartDataPoint!]!
  success: Boolean!
  message: String
}

# Chart data point for transaction volume
type TransactionVolumeChartDataPoint {
  timestamp: Time!
  period: String!
  contract: String!
  meme: String!
  all: String!
}

# Current values for transaction volume chart
type TransactionVolumeCurrentValues {
  all: String!
  meme: String!
  contract: String!
}

# Response for transaction volume chart
type TransactionVolumeChartResponse {
  currentValues: TransactionVolumeCurrentValues!
  data: [TransactionVolumeChartDataPoint!]!
  success: Boolean!
  message: String
}

# Chart data point for invitation count
type InvitationCountChartDataPoint {
  timestamp: Time!
  period: String!
  value: String!
}

# Current values for invitation count chart
type InvitationCountCurrentValues {
  all: String!
}

# Response for invitation count chart
type InvitationCountChartResponse {
  currentValues: InvitationCountCurrentValues!
  all: [InvitationCountChartDataPoint!]!
  success: Boolean!
  message: String
}
